{"name": "mattermost-mobile", "version": "2.20.0", "description": "Mattermost Mobile with React Native", "repository": "**************:mattermost/mattermost-mobile.git", "author": "Mattermost, Inc.", "license": "Apache 2.0", "private": true, "engines": {"node": "^18.18.0 || ^20.0.0", "npm": "^9 || ^10"}, "dependencies": {"@amplitude/react-native": "2.17.3", "@braintree/sanitize-url": "7.1.0", "@carchaze/react-native-voice-message-player": "1.0.5", "@formatjs/intl-datetimeformat": "6.12.5", "@formatjs/intl-getcanonicallocales": "2.3.0", "@formatjs/intl-listformat": "7.5.7", "@formatjs/intl-locale": "4.0.0", "@formatjs/intl-numberformat": "8.10.3", "@formatjs/intl-pluralrules": "5.2.14", "@giphy/react-native-sdk": "3.2.6", "@gorhom/bottom-sheet": "4.6.4", "@jitsi/react-native-sdk": "10.2.1", "@killerwink/lottie-react-native-color": "1.0.2", "@mattermost/calls": "github:mattermost/calls-common#1ce6defb1ee0c1e0f106ddff8f46c37d10d60b76", "@mattermost/compass-icons": "0.1.45", "@mattermost/hardware-keyboard": "file:./libraries/@mattermost/hardware-keyboard", "@mattermost/keyboard-tracker": "file:./libraries/@mattermost/keyboard-tracker", "@mattermost/react-native-emm": "1.5.0", "@mattermost/react-native-network-client": "1.7.2", "@mattermost/react-native-paste-input": "0.8.0", "@mattermost/react-native-turbo-log": "0.4.0", "@mattermost/rnshare": "file:./libraries/@mattermost/rnshare", "@mattermost/rnutils": "file:./libraries/@mattermost/rnutils", "@msgpack/msgpack": "2.8.0", "@nozbe/watermelondb": "0.27.1", "@react-native-async-storage/async-storage": "2.0.0", "@react-native-camera-roll/camera-roll": "7.8.3", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/blur": "4.4.1", "@react-native-community/datetimepicker": "8.2.0", "@react-native-community/netinfo": "11.3.2", "@react-native-community/slider": "4.5.6", "@react-native-cookies/cookies": "6.2.1", "@react-native-google-signin/google-signin": "10.1.0", "@react-native/metro-config": "*", "@react-navigation/bottom-tabs": "6.6.1", "@react-navigation/native": "6.1.18", "@react-navigation/stack": "6.4.1", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "@sentry/react-native": "5.27.0", "@stream-io/flat-list-mvcp": "0.10.3", "@voximplant/react-native-foreground-service": "3.0.2", "alhashedi-react-native-image-editor": "0.7.4", "assert": "2.1.0", "base-64": "1.0.0", "commonmark": "npm:@mattermost/commonmark@0.30.1-3", "commonmark-react-renderer": "github:mattermost/commonmark-react-renderer#81b5d27509652bae50b4b510ede777dd3bd923cf", "crypto-js": "4.2.0", "deep-equal": "2.2.3", "deepmerge": "4.3.1", "emoji-regex": "10.3.0", "expo": "51.0.24", "expo-application": "5.9.1", "expo-av": "14.0.7", "expo-blur": "~13.0.2", "expo-crypto": "13.0.2", "expo-device": "6.0.2", "expo-file-system": "17.0.1", "expo-image": "1.12.13", "expo-linear-gradient": "13.0.2", "expo-local-authentication": "~14.0.1", "expo-location": "17.0.1", "expo-store-review": "7.0.2", "expo-video-thumbnails": "8.0.0", "expo-web-browser": "13.0.3", "fflate": "0.8.2", "fuse.js": "7.0.0", "html-entities": "2.5.2", "http": "0.0.1-security", "i": "0.3.7", "install": "0.13.0", "lottie-ios": "4.5.0", "lottie-react-native": "7.0.0", "manage-external-storage": "0.1.3", "mime-db": "1.53.0", "moment-timezone": "0.5.45", "node-html-parser": "6.1.13", "npm": "10.9.0", "pako": "2.1.0", "path-to-regexp": "7.1.0", "react": "18.2.0", "react-freeze": "1.0.4", "react-intl": "6.6.8", "react-native": "0.74.5", "react-native-arabic-numbers": "1.0.1", "react-native-audio-recorder-player": "3.6.11", "react-native-background-timer": "2.4.1", "react-native-calendar-events": "2.2.0", "react-native-circular-progress-indicator": "4.4.2", "react-native-cli": "^2.0.1", "react-native-copilot": "3.3.2", "react-native-datepicker": "1.7.2", "react-native-default-preference": "1.4.4", "react-native-device-info": "14.0.4", "react-native-document-picker": "9.3.0", "react-native-dotenv": "3.4.11", "react-native-emoji-selector": "0.2.0", "react-native-exception-handler": "2.10.10", "react-native-file-viewer": "2.1.5", "react-native-fs": "2.20.0", "react-native-gesture-handler": "2.20.0", "react-native-get-random-values": "1.11.0", "react-native-haptic-feedback": "2.2.0", "react-native-heroicons": "4.0.0", "react-native-image-pan-zoom": "2.1.12", "react-native-image-picker": "7.1.2", "react-native-immersive-mode": "2.0.2", "react-native-incall-manager": "4.2.0", "react-native-keep-awake": "4.0.0", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-keychain": "8.2.0", "react-native-localize": "3.2.1", "react-native-math-view": "3.9.5", "react-native-navigation": "7.40.1", "react-native-notifications": "5.1.0", "react-native-orientation-locker": "1.6.0", "react-native-otp-entry": "1.8.3", "react-native-pager-view": "6.2.0", "react-native-paper": "5.12.5", "react-native-performance": "5.1.2", "react-native-permissions": "4.1.5", "react-native-progress": "5.0.1", "react-native-push-notification": "8.1.1", "react-native-reanimated": "3.15.1", "react-native-root-checker": "0.4.0", "react-native-safe-area-context": "4.10.8", "react-native-screens": "3.34.0", "react-native-section-list-get-item-layout": "2.2.3", "react-native-shadow-2": "7.1.0", "react-native-share": "10.2.1", "react-native-slider-custom": "0.11.0", "react-native-sound": "0.11.2", "react-native-splash-screen": "3.3.0", "react-native-svg": "15.4.0", "react-native-swipe-gestures-plus": "1.1.3", "react-native-track-player": "4.1.1", "react-native-translator": "1.2.0", "react-native-uuid": "2.0.2", "react-native-vector-icons": "10.1.0", "react-native-video": "6.10.2", "react-native-video-duration": "0.1.2", "react-native-walkthrough-tooltip": "1.6.0", "react-native-watch-connectivity": "1.1.0", "react-native-webrtc": "124.0.3", "react-native-webview": "13.8.7", "react-syntax-highlighter": "15.5.0", "react-with-direction": "1.4.0", "semver": "7.6.3", "text-encoding": "0.7.0", "tinycolor2": "1.6.0", "url": "0.11.4", "url-parse": "1.5.10", "zustand": "5.0.0-rc.2"}, "devDependencies": {"@babel/cli": "7.24.8", "@babel/core": "7.25.2", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.24.7", "@babel/plugin-transform-flow-strip-types": "7.25.2", "@babel/plugin-transform-runtime": "7.24.7", "@babel/preset-env": "7.25.3", "@babel/preset-typescript": "7.24.7", "@babel/register": "7.24.6", "@babel/runtime": "7.25.0", "@react-native/babel-preset": "0.74.87", "@react-native/eslint-config": "0.74.87", "@react-native/metro-config": "0.74.87", "@react-native/typescript-config": "0.74.87", "@testing-library/react-hooks": "8.0.1", "@testing-library/react-native": "12.5.2", "@types/base-64": "1.0.2", "@types/commonmark": "0.27.9", "@types/commonmark-react-renderer": "4.3.4", "@types/deep-equal": "1.0.4", "@types/jest": "29.5.12", "@types/lodash": "4.17.7", "@types/mime-db": "1.43.5", "@types/pako": "2.0.3", "@types/querystringify": "2.0.2", "@types/react": "18.3.3", "@types/react-native-background-timer": "2.0.2", "@types/react-native-dotenv": "0.2.2", "@types/react-native-share": "3.3.8", "@types/react-syntax-highlighter": "15.5.13", "@types/semver": "7.5.8", "@types/tinycolor2": "1.4.6", "@types/tough-cookie": "4.0.5", "@types/url-parse": "1.4.11", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "7.13.1", "@typescript-eslint/parser": "7.13.1", "axios": "1.7.5", "axios-cookiejar-support": "5.0.2", "babel-jest": "29.7.0", "babel-plugin-module-resolver": "5.0.2", "detox": "20.25.2", "eslint": "8.57.0", "eslint-plugin-header": "3.1.1", "eslint-plugin-import": "2.29.1", "eslint-plugin-jest": "28.7.0", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "4.6.2", "husky": "9.1.4", "isomorphic-fetch": "3.0.0", "jest": "29.7.0", "jest-cli": "29.7.0", "jest-expo": "51.0.3", "jetifier": "2.0.0", "mmjstool": "github:mattermost/mattermost-utilities#83b1b311972b8f5e750aae4019457a40abb5aa44", "nock": "13.5.4", "obfuscator-io-metro-plugin": "2.1.3", "patch-package": "8.0.0", "react-devtools-core": "5.3.1", "tough-cookie": "4.1.4", "ts-jest": "29.2.4", "typescript": "5.5.4", "uuid": "10.0.0"}, "scripts": {"android": "react-native run-android", "build:android": "./scripts/build.sh apk", "build:android-unsigned": "./scripts/build.sh apk unsigned", "build:ios": "./scripts/build.sh ipa", "build:ios-sim": "./scripts/build.sh ipa simulator", "build:ios-unsigned": "./scripts/build.sh ipa unsigned", "check": "npm run lint && npm run tsc", "check-test": "npm run lint && npm run tsc && npm run test", "clean": "./scripts/clean.sh", "e2e:android": "cd detox && npm run e2e:android-build && npm run e2e:android-test && cd ..", "e2e:ios": "cd detox && npm run e2e:ios-test && cd ..", "fix": "npm run lint -- --fix", "i18n-clean-empty": "npm run mmjstool -- i18n clean-empty --mobile-dir .", "i18n-extract": "npm run mmjstool -- i18n extract-mobile", "ios": "react-native run-ios", "ios-gems": "bundle install", "ios-gems-m1": "arch -arm64 bundle install", "lint": "eslint --ignore-path .gitignore --ignore-pattern node_modules --quiet .", "mmjstool": "mmjstool", "pod-install": "cd ios && RCT_NEW_ARCH_ENABLED=0 pod install", "pod-install-m1": "cd ios && RCT_NEW_ARCH_ENABLED=0 arch -x86_64 pod install", "postinstall": "patch-package && ./scripts/postinstall.sh", "prepare": "husky", "preinstall": "./scripts/preinstall.sh && npx solidarity", "start": "react-native start", "test": "jest --forceExit --runInBand", "test:coverage": "jest --coverage", "test:watch": "npm test -- --watch", "tsc": "NODE_OPTIONS=--max_old_space_size=12000 tsc --noEmit", "updatesnapshot": "jest --updateSnapshot"}, "overrides": {"@testing-library/react-native": {"react": "^18.2.0"}, "@testing-library/react-hooks": {"@types/react": "^18.2.37", "react": "^18.2.0", "react-test-renderer": "^18.2.0"}, "@react-native-community/datetimepicker": {"react-native": "^0.74.2"}, "@react-native/eslint-config": {"@typescript-eslint/eslint-plugin": "^7.12.0", "@typescript-eslint/parser": "^7.12.0"}, "@react-native-clipboard/clipboard": {"react-native": "^0.74.2"}, "react-native-windows": {"react-native": "^0.74.2"}, "@jitsi/react-native-sdk": {"@giphy/react-native-sdk": "3.2.6", "@react-native-async-storage/async-storage": "2.0.0", "@react-native-clipboard/clipboard": "1.14.1", "@react-native-community/netinfo": "11.3.2", "@react-native-community/slider": "4.5.6", "react-native-device-info": "14.0.4", "react-native-get-random-values": "1.11.0", "react-native-gesture-handler": "2.20.0", "react-native-pager-view": "6.2.0", "react-native-performance": "5.1.2", "react-native-safe-area-context": "4.10.8", "react-native-screens": "3.34.0", "react-native-svg": "15.4.0", "react-native-video": "6.10.2", "react-native-webrtc": "124.0.3", "react-native-webview": "13.8.7"}}, "expo": {"autolinking": {"exclude": ["expo-keep-awake", "expo-asset", "expo-font"]}}}