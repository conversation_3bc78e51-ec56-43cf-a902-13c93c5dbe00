// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

import {useTheme} from '@context/theme';
import {observePostAuthor} from '@queries/servers/post';
import {withDatabase, withObservables} from '@nozbe/watermelondb/react';
import {typography} from '@utils/typography';

import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';
import type {WithDatabaseArgs} from '@typings/database/database';

type MessageUserNameProps = {
    author?: UserModel;
    post: PostModel;
    isCurrentUser: boolean;
};

/**
 * Capitalizes the first English letter of a name while preserving:
 * - Non-English characters
 * - Existing proper capitalization
 * - Special characters and numbers
 *
 * @param name - The name to capitalize
 * @returns The name with the first English letter capitalized
 */
const capitalizeFirstEnglishLetter = (name: string): string => {
    if (!name || typeof name !== 'string') {
        return name || '';
    }

    // Find the first English letter (A-Z, a-z)
    const firstEnglishLetterMatch = name.match(/[a-zA-Z]/);

    if (!firstEnglishLetterMatch) {
        // No English letters found, return as is
        return name;
    }

    const firstEnglishLetterIndex = firstEnglishLetterMatch.index!;
    const firstEnglishLetter = firstEnglishLetterMatch[0];

    // If the first English letter is already uppercase, return as is
    if (firstEnglishLetter === firstEnglishLetter.toUpperCase()) {
        return name;
    }

    // Capitalize the first English letter
    return name.substring(0, firstEnglishLetterIndex) +
           firstEnglishLetter.toUpperCase() +
           name.substring(firstEnglishLetterIndex + 1);
};

const MessageUserName = ({
    author,
    post,
    isCurrentUser,
}: MessageUserNameProps) => {
    const theme = useTheme();

    // Don't show name if no author
    if (!author) {
        return null;
    }

    // Get the full name (first + last name)
    const firstName = author.firstName || '';
    const lastName = author.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();
    
    // If no first/last name, fall back to username or display name
    const displayName = fullName || author.username || author.nickname || 'Unknown User';

    const styles = StyleSheet.create({
        container: {
            height: 20,
            maxWidth: '100%',
            borderRadius: 10,

        },
        nameText: {
            color: theme.centerChannelColor,
            opacity: 0.7,
            ...typography('Body', 75, 'SemiBold'),
            fontSize: 14,
            textAlign: isCurrentUser ? 'right' : 'left',
            marginBottom: -2,
        },
    });

    return (
        <View style={[styles.container, {paddingLeft: isCurrentUser ? 10 : 0, paddingRight: isCurrentUser ? 0 : 10, paddingBottom: isCurrentUser ? 0 : -30,paddingTop: isCurrentUser ? 5 : -10}]}>
            <Text
                style={styles.nameText}
                numberOfLines={1}
                ellipsizeMode="tail"
                testID={`message_user_name.${author.id}.name`}
            >
                {displayName}
            </Text>
        </View>
    );
};

const withMessageUserName = withObservables(['post'], ({database, post}: {post: PostModel} & WithDatabaseArgs) => {
    const author = observePostAuthor(database, post);

    return {
        author,
    };
});

export default withDatabase(withMessageUserName(MessageUserName));
