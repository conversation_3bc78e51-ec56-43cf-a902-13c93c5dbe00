// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React from 'react';
import {View, Text, StyleSheet} from 'react-native';

import {useTheme} from '@context/theme';
import {observePostAuthor} from '@queries/servers/post';
import {withDatabase, withObservables} from '@nozbe/watermelondb/react';
import {typography} from '@utils/typography';

import type PostModel from '@typings/database/models/servers/post';
import type UserModel from '@typings/database/models/servers/user';
import type {WithDatabaseArgs} from '@typings/database/database';

type MessageUserNameProps = {
    author?: UserModel;
    post: PostModel;
    isCurrentUser: boolean;
};

const MessageUserName = ({
    author,
    post,
    isCurrentUser,
}: MessageUserNameProps) => {
    const theme = useTheme();

    // Don't show name if no author
    if (!author) {
        return null;
    }

    // Get the full name (first + last name)
    const firstName = author.firstName || '';
    const lastName = author.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();
    
    // If no first/last name, fall back to username or display name
    const displayName = fullName || author.username || author.nickname || 'Unknown User';

    const styles = StyleSheet.create({
        container: {
            paddingHorizontal: 16,
            marginTop: -5,
            // paddingBottom: -2,
        },
        nameText: {
            color: theme.centerChannelColor,
            opacity: 0.8,
            ...typography('Body', 100, 'SemiBold'),
            textAlign: isCurrentUser ? 'right' : 'left',
        },
    });

    return (
        <View style={styles.container}>
            <Text
                style={styles.nameText}
                numberOfLines={1}
                ellipsizeMode="tail"
                testID={`message_user_name.${author.id}.name`}
            >
                {displayName}
            </Text>
        </View>
    );
};

const withMessageUserName = withObservables(['post'], ({database, post}: {post: PostModel} & WithDatabaseArgs) => {
    const author = observePostAuthor(database, post);

    return {
        author,
    };
});

export default withDatabase(withMessageUserName(MessageUserName));
